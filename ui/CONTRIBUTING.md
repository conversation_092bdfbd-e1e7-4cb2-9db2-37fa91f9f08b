# 贡献指南

感谢您对 Genie UI 项目的关注！我们欢迎所有形式的贡献，无论是新功能、bug 修复、文档改进，还是其他任何形式的帮助。

## 如何贡献

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的改动 (`git commit -m 'Add some AmazingFeature'`)
4. 将您的改动推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

## 开发设置

1. 克隆您 fork 的仓库
2. 安装依赖：`pnpm install`
3. 启动开发服务器：`pnpm dev`

## 代码规范

- 我们使用 ESLint 和 Prettier 来保持代码风格的一致性
- 提交代码前，请运行 `pnpm lint` 和 `pnpm fix` 来检查和修复代码风格问题
- 确保您的代码通过了所有的测试

## 提交 Pull Request

1. 确保您的 PR 描述清楚地说明了您的改动
2. 如果您的 PR 解决了一个 issue，请在 PR 描述中引用该 issue
3. 确保您的代码与主分支没有冲突
4. 等待维护者的审核和反馈

## 报告 Bug

如果您发现了 bug，请在 issue 区创建一个新的 issue，并尽可能详细地描述：

- 问题的详细描述
- 复现步骤
- 预期行为
- 实际行为
- 截图（如果适用）
- 您的环境信息（操作系统、浏览器版本等）

## 提出新功能

如果您有新功能的想法，我们也非常欢迎！请创建一个新的 issue，描述您的想法和可能的实现方式。

再次感谢您的贡献！
