# OpenAI 配置 (litellm 可能需要这些作为默认值)

# Gemini 配置 (通过 OpenAI 兼容接口)
OPENAI_API_KEY=AIzaSyAJcq8DQP8U7Ol-tTtOKLO0UDGhM4TD1oQ
OPENAI_BASE_URL=https://gemini.afan.host/v1/

# 如果使用deepseek，请使用如下变量


# <a href="https://docs.litellm.ai/docs/providers">其他模型支持文档</a>
# ANTHROPIC_API_KEY=<or anthropic api key>
# ANTHROPIC_API_BASE=<your base url>

# 敏感词过滤
SENSITIVE_WORD_REPLACE=true

# 文件系统路径配置
FILE_SAVE_PATH=file_db_dir
SQLITE_DB_PATH=autobots.db
FILE_SERVER_URL=http://**************:1601/v1/file_tool

# DeepSearch 配置
USE_JD_SEARCH_GATEWAY=false
USE_SEARCH_ENGINE=serp
SEARCH_COUNT=10
SEARCH_TIMEOUT=10
SEARCH_THREAD_NUM=5

DEFAULT_MODEL=gemini/gemini-2.0-flash-exp

QUERY_DECOMPOSE_MODEL=gemini/gemini-2.0-flash-exp
QUERY_DECOMPOSE_THINK_MODEL=gemini/gemini-2.0-flash-exp
QUERY_DECOMPOSE_MAX_SIZE=5
SEARCH_REASONING_MODEL=gemini/gemini-2.0-flash-exp
SEARCH_ANSWER_MODEL=gemini/gemini-2.0-flash-exp
SEARCH_ANSWER_LENGTH=10000
REPORT_MODEL=gemini/gemini-2.0-flash-exp

SINGLE_PAGE_MAX_SIZE=0

BING_SEARCH_URL=
BING_SEARCH_API_KEY=

JINA_SEARCH_URL=https://s.jina.ai/
JINA_SEARCH_API_KEY=

SOGOU_SEARCH_URL=
SOGOU_SEARCH_API_KEY=

SERPER_SEARCH_URL=https://google.serper.dev/search
SERPER_SEARCH_API_KEY=3e5800532c5eee5ebcaa213a0b3f4642cd6b6d1d

# Code Interpreter 配置
CODE_INTEPRETER_MODEL=gemini/gemini-2.0-flash-exp
